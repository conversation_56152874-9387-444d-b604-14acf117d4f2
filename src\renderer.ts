import { State } from "./state";
import enemiesPath from "./sprites/enemies.png";

const TILE_PIXEL_SIZE = 32;

const MAIN_CANVAS_TILE_SIZE = 13;

async function loadImage(path: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = path;
  });
}

const spriteCanvas = document.createElement("canvas");
const spriteCtx = spriteCanvas.getContext("2d")!;
async function loadSprites(path: string, size: number): Promise<ImageData[][]> {
  const img = await loadImage(path);
  spriteCanvas.width = img.width;
  spriteCanvas.height = img.height;
  spriteCtx.drawImage(img, 0, 0);

  const spriteSheet = [];
  for (let i = 0; i < img.height / size; i++) {
    const spriteRow = [];
    for (let j = 0; j < img.width / size; j++) {
      const frame = spriteCtx.getImageData(j * size, i * size, size, size);
      spriteRow.push(frame);
    }
    spriteSheet.push(spriteRow);
  }
  return spriteSheet;
}

export interface RendererOptions {
  mainCanvas: HTMLCanvasElement;

  animationSpeed?: number;
}

export class Renderer {
  mainCanvas: HTMLCanvasElement;
  mainCtx: CanvasRenderingContext2D;

  animationSpeed: number;

  isInitialized = false;
  intializePromise: Promise<void> | null = null;

  /**
   * array of sprite sheets
   * order is the same as sprite in the image
   * each sprite sheet is an array of ImageData representing the frames
   */
  sprites: ImageData[][] = [];

  constructor(settings: RendererOptions) {
    const { mainCanvas, animationSpeed } = { animationSpeed: 300, ...settings };

    this.mainCanvas = mainCanvas;
    this.mainCanvas.width = MAIN_CANVAS_TILE_SIZE * TILE_PIXEL_SIZE;
    this.mainCanvas.height = MAIN_CANVAS_TILE_SIZE * TILE_PIXEL_SIZE;
    this.mainCtx = this.mainCanvas.getContext("2d")!;

    this.animationSpeed = animationSpeed;

    this.intializePromise = this._init();
  }

  async _init() {
    this.sprites = this.sprites.concat(await loadSprites(enemiesPath, 32));
    this.isInitialized = true;
  }

  render(state: State) {
    
  }
}
